{"cells": [{"cell_type": "code", "execution_count": 1, "id": "b9c08c27", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_8540\\296679825.py:17: DtypeWarning: Columns (122) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  df = pd.read_csv(csv_file)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "文件 'cb_factors2.csv' 中列 'open' 的分析结果:\n", "\n", "最大的10个值:\n", "1. 行 285233: 3330.0\n", "2. 行 285234: 3322.0\n", "3. 行 285235: 3317.98\n", "4. 行 285236: 3222.0\n", "5. 行 285232: 3133.0\n", "6. 行 285229: 3130.0\n", "7. 行 285231: 3060.0\n", "8. 行 285227: 3021.0\n", "9. 行 285226: 2980.01\n", "10. 行 285237: 2955.0\n", "\n", "最小的10个值:\n", "1. 行 275631: 25.42\n", "2. 行 275639: 25.51\n", "3. 行 275633: 25.6\n", "4. 行 275636: 25.65\n", "5. 行 275635: 25.8\n", "6. 行 275638: 25.8\n", "7. 行 275632: 26.1\n", "8. 行 275637: 26.2\n", "9. 行 275634: 26.3\n", "10. 行 275630: 26.8\n"]}], "source": ["import pandas as pd\n", "\n", "def find_extreme_values(csv_file, column_name, num_extremes=10):\n", "    \"\"\"\n", "    读取CSV文件并找出指定列中最大和最小的10个数字\n", "    \n", "    参数:\n", "        csv_file (str): CSV文件路径\n", "        column_name (str): 要分析的列名\n", "        num_extremes (int): 要查找的最大/最小值数量(默认为10)\n", "        \n", "    返回:\n", "        dict: 包含最大和最小值的字典\n", "    \"\"\"\n", "    try:\n", "        # 读取CSV文件\n", "        df = pd.read_csv(csv_file)\n", "        \n", "        # 检查列是否存在\n", "        if column_name not in df.columns:\n", "            raise ValueError(f\"列 '{column_name}' 不存在于CSV文件中\")\n", "            \n", "        # 转换为数值类型(自动处理非数值数据)\n", "        series = pd.to_numeric(df[column_name], errors='coerce')\n", "        \n", "        # 删除NaN值\n", "        clean_series = series.dropna()\n", "        \n", "        if len(clean_series) == 0:\n", "            raise ValueError(\"该列没有有效的数值数据\")\n", "            \n", "        # 获取最大的10个值\n", "        top_values = clean_series.nlargest(num_extremes)\n", "        \n", "        # 获取最小的10个值\n", "        bottom_values = clean_series.nsmallest(num_extremes)\n", "        \n", "        return {\n", "            'top_values': top_values.to_dict(),\n", "            'bottom_values': bottom_values.to_dict(),\n", "            'column': column_name,\n", "            'file': csv_file\n", "        }\n", "        \n", "    except Exception as e:\n", "        print(f\"发生错误: {str(e)}\")\n", "        return None\n", "\n", "# 使用示例\n", "if __name__ == \"__main__\":\n", "    # 替换为你的CSV文件路径和列名\n", "    file_path = \"cb_factors2.csv\"  \n", "    column_to_analyze = \"open\"  \n", "    \n", "    results = find_extreme_values(file_path, column_to_analyze)\n", "    \n", "    if results:\n", "        print(f\"\\n文件 '{results['file']}' 中列 '{results['column']}' 的分析结果:\")\n", "        \n", "        print(\"\\n最大的10个值:\")\n", "        for rank, (index, value) in enumerate(results['top_values'].items(), 1):\n", "            print(f\"{rank}. 行 {index}: {value}\")\n", "            \n", "        print(\"\\n最小的10个值:\")\n", "        for rank, (index, value) in enumerate(results['bottom_values'].items(), 1):\n", "            print(f\"{rank}. 行 {index}: {value}\")"]}, {"cell_type": "code", "execution_count": 6, "id": "0a36a934", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["原始数据行数: 490797\n", "过滤后数据行数: 490796\n", "删除了 1 行\n", "结果已保存到: cb_factors3.csv\n"]}], "source": ["import pandas as pd\n", "\n", "def filter_csv_by_column_value(input_file, output_file, column_name, threshold):\n", "    \"\"\"\n", "    删除指定列中数值小于阈值的所有行\n", "    \n", "    参数:\n", "        input_file (str): 输入CSV文件路径\n", "        output_file (str): 输出CSV文件路径\n", "        column_name (str): 要过滤的列名\n", "        threshold (float): 阈值，小于此值的行将被删除\n", "    \"\"\"\n", "    try:\n", "        # 读取CSV文件\n", "        df = pd.read_csv(input_file)\n", "        \n", "        print(f\"原始数据行数: {len(df)}\")\n", "        \n", "        # 检查列是否存在\n", "        if column_name not in df.columns:\n", "            raise ValueError(f\"列 '{column_name}' 不存在于CSV文件中\")\n", "            \n", "        # 将指定列转换为数值类型(非数值转为NaN)\n", "        df[column_name] = pd.to_numeric(df[column_name], errors='coerce')\n", "        \n", "        # 删除小于阈值的行\n", "        filtered_df = df[df[column_name] >= threshold]\n", "        \n", "        # 删除转换过程中产生的NaN行(可选)\n", "        filtered_df = filtered_df.dropna(subset=[column_name])\n", "        \n", "        print(f\"过滤后数据行数: {len(filtered_df)}\")\n", "        print(f\"删除了 {len(df) - len(filtered_df)} 行\")\n", "        \n", "        # 保存到新文件\n", "        filtered_df.to_csv(output_file, index=False)\n", "        print(f\"结果已保存到: {output_file}\")\n", "        \n", "    except Exception as e:\n", "        print(f\"发生错误: {str(e)}\")\n", "\n", "# 使用示例\n", "if __name__ == \"__main__\":\n", "    # 替换为你的文件路径和参数\n", "    input_csv = \"cb_factors2.csv\"      # 输入文件\n", "    output_csv = \"cb_factors3.csv\"    # 输出文件\n", "    column = \"open\"            # 要过滤的列名\n", "    min_value = 1             # 阈值\n", "    \n", "    filter_csv_by_column_value(input_csv, output_csv, column, min_value)"]}, {"cell_type": "code", "execution_count": 2, "id": "72d00386", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["处理完成，结果已保存到 cb_factors1.csv\n", "原始行数: 545285，处理后行数: 495223\n"]}], "source": ["import pandas as pd\n", "\n", "def remove_rows_with_empty_columns(input_file, output_file, columns_to_check):\n", "    \"\"\"\n", "    删除指定列中为空值的整行\n", "    \n", "    参数:\n", "        input_file: 输入CSV文件路径\n", "        output_file: 输出CSV文件路径\n", "        columns_to_check: 要检查的列名列表或单个列名\n", "    \"\"\"\n", "    # 读取CSV文件\n", "    df = pd.read_csv(input_file)\n", "    \n", "    # 如果传入的是单个列名，转换为列表\n", "    if isinstance(columns_to_check, str):\n", "        columns_to_check = [columns_to_check]\n", "    \n", "    # 删除指定列中任何一列为空值的行\n", "    df_cleaned = df.dropna(subset=columns_to_check)\n", "    \n", "    # 保存处理后的数据\n", "    df_cleaned.to_csv(output_file, index=False)\n", "    print(f\"处理完成，结果已保存到 {output_file}\")\n", "    print(f\"原始行数: {len(df)}，处理后行数: {len(df_cleaned)}\")\n", "\n", "# 使用示例\n", "if __name__ == \"__main__\":\n", "    input_csv = \"cb_factors.csv\"      # 输入文件路径\n", "    output_csv = \"cb_factors1.csv\"    # 输出文件路径\n", "    columns = \"cb_momentum_60d\"  # 要检查的列名列表\n", "    \n", "    # 也可以只检查单个列\n", "    # columns = \"column1\"\n", "    # 多个列\n", "    #[\"column1\", \"column2\"] \n", "    remove_rows_with_empty_columns(input_csv, output_csv, columns)"]}, {"cell_type": "code", "execution_count": 9, "id": "af0f8f32", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["处理完成，结果已保存到 cb_factors4.csv\n", "列 'cb_implied_volatility' 中共有 179399 个空值被替换为0\n"]}], "source": ["import pandas as pd\n", "\n", "def fill_empty_with_zero(input_file, output_file, column_name):\n", "    \"\"\"\n", "    将CSV文件中指定列的缺失值补0\n", "    \n", "    参数:\n", "        input_file: 输入CSV文件路径\n", "        output_file: 输出CSV文件路径\n", "        column_name: 要处理的列名\n", "    \"\"\"\n", "    # 读取CSV文件\n", "    df = pd.read_csv(input_file)\n", "    \n", "    # 统计补0前的空值数量\n", "    null_count_before = df[column_name].isnull().sum()\n", "    \n", "    # 将指定列的空值替换为0\n", "    df[column_name] = df[column_name].fillna(0)\n", "    \n", "    # 保存处理后的数据\n", "    df.to_csv(output_file, index=False)\n", "    \n", "    # 输出处理结果\n", "    print(f\"处理完成，结果已保存到 {output_file}\")\n", "    print(f\"列 '{column_name}' 中共有 {null_count_before} 个空值被替换为0\")\n", "\n", "# 使用示例\n", "if __name__ == \"__main__\":\n", "    input_csv = \"cb_factors3.csv\"      # 输入文件路径\n", "    output_csv = \"cb_factors4.csv\"    # 输出文件路径\n", "    column = \"cb_implied_volatility\"  # 要处理的列名\n", "    \n", "    fill_empty_with_zero(input_csv, output_csv, column)"]}, {"cell_type": "code", "execution_count": 2, "id": "a6b67cae", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["==================================================\n", "数据分析报告\n", "==================================================\n", "\n", "1. 文件中没有缺失值\n", "\n", "2. 文件中没有inf值\n", "\n", "3. trade_date列中最新的日期是: 2025-05-15 00:00:00\n", "\n", "3. trade_date列中最新的日期是: 2006-06-01 00:00:00\n"]}], "source": ["import pandas as pd\n", "import numpy as np\n", "\n", "def analyze_data(csv_file):\n", "    \"\"\"\n", "    检查CSV文件中所有列的缺失值和inf值，并报告trade_date列中最新的日期\n", "    \n", "    参数:\n", "        csv_file: 要检查的CSV文件路径\n", "    \"\"\"\n", "    # 读取CSV文件\n", "    df = pd.read_csv(csv_file)\n", "    \n", "    # 1. 检查缺失值\n", "    missing_data = {}\n", "    for column in df.columns:\n", "        null_rows = df[df[column].isnull()].index.tolist()\n", "        if null_rows:\n", "            missing_data[column] = [row + 1 for row in null_rows]  # +1因为索引从0开始\n", "    \n", "    # 2. 检查inf值\n", "    inf_data = {}\n", "    for column in df.select_dtypes(include=[np.number]).columns:  # 只检查数值列\n", "        inf_rows = df[np.isinf(df[column])].index.tolist()\n", "        if inf_rows:\n", "            inf_data[column] = [row + 1 for row in inf_rows]\n", "    \n", "    # 3. 检查trade_date列中最新的日期\n", "    latest_date = None\n", "    if 'trade_date' in df.columns:\n", "        try:\n", "            # 尝试转换为日期格式\n", "            df['trade_date'] = pd.to_datetime(df['trade_date'])\n", "            latest_date = df['trade_date'].max()\n", "        except:\n", "            # 如果转换失败，直接取最大值\n", "            latest_date = df['trade_date'].max()\n", "    oldest_date = None\n", "    if 'trade_date' in df.columns:\n", "        try:\n", "            # 尝试转换为日期格式\n", "            df['trade_date'] = pd.to_datetime(df['trade_date'])\n", "            oldest_date = df['trade_date'].min()\n", "        except:\n", "            # 如果转换失败，直接取最大值\n", "            oldest_date = df['trade_date'].min()\n", "    # 输出结果\n", "    print(\"=\"*50)\n", "    print(\"数据分析报告\")\n", "    print(\"=\"*50)\n", "    \n", "    # 缺失值报告\n", "    if not missing_data:\n", "        print(\"\\n1. 文件中没有缺失值\")\n", "    else:\n", "        print(\"\\n1. 发现以下缺失值:\")\n", "        for column, rows in missing_data.items():\n", "            print(f\"\\n列名: {column}\")\n", "            print(f\"缺失行数: {len(rows)}\")\n", "            print(f\"具体行号: {rows}\")\n", "    \n", "    # inf值报告\n", "    if not inf_data:\n", "        print(\"\\n2. 文件中没有inf值\")\n", "    else:\n", "        print(\"\\n2. 发现以下inf值:\")\n", "        for column, rows in inf_data.items():\n", "            print(f\"\\n列名: {column}\")\n", "            print(f\"包含inf的行数: {len(rows)}\")\n", "            print(f\"具体行号: {rows}\")\n", "    \n", "    # 最新日期报告\n", "    if latest_date is not None:\n", "        print(f\"\\n3. trade_date列中最新的日期是: {latest_date}\")\n", "    else:\n", "        print(\"\\n3. 文件中没有trade_date列\")\n", "    if oldest_date is not None:\n", "        print(f\"\\n3. trade_date列中最新的日期是: {oldest_date}\")\n", "    else:\n", "        print(\"\\n3. 文件中没有trade_date列\")\n", "\n", "if __name__ == \"__main__\":\n", "    file_path = \"cb_factors.csv\"\n", "    analyze_data(file_path)"]}, {"cell_type": "code", "execution_count": 12, "id": "64af7b33", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["处理完成，结果已保存到: cb_factors6.csv\n", "原始行数: 492374\n", "删除行数: 660\n", "保留行数: 491714\n", "删除的行占总行数的: 0.13%\n"]}], "source": ["import pandas as pd\n", "\n", "def remove_rows_with_any_missing(input_file, output_file):\n", "    \"\"\"\n", "    删除CSV文件中任何列包含缺失值的整行\n", "    \n", "    参数:\n", "        input_file: 输入CSV文件路径\n", "        output_file: 输出CSV文件路径\n", "    \"\"\"\n", "    # 读取CSV文件\n", "    df = pd.read_csv(input_file)\n", "    \n", "    # 记录原始行数\n", "    original_rows = len(df)\n", "    \n", "    # 删除任何列包含缺失值的行\n", "    df_cleaned = df.dropna(how='any')\n", "    \n", "    # 保存处理后的数据\n", "    df_cleaned.to_csv(output_file, index=False)\n", "    \n", "    # 计算删除的行数\n", "    removed_rows = original_rows - len(df_cleaned)\n", "    \n", "    # 输出结果\n", "    print(f\"处理完成，结果已保存到: {output_file}\")\n", "    print(f\"原始行数: {original_rows}\")\n", "    print(f\"删除行数: {removed_rows}\")\n", "    print(f\"保留行数: {len(df_cleaned)}\")\n", "    print(f\"删除的行占总行数的: {removed_rows/original_rows:.2%}\")\n", "\n", "# 使用示例\n", "if __name__ == \"__main__\":\n", "    input_csv = \"cb_factors5.csv\"      # 输入文件路径\n", "    output_csv = \"cb_factors6.csv\"    # 输出文件路径\n", "    \n", "    remove_rows_with_any_missing(input_csv, output_csv)"]}, {"cell_type": "code", "execution_count": 1, "id": "9950d02f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["发现 733 行包含inf/-inf值\n", "\n", "处理完成，结果已保存到: cb_factors6.csv\n", "原始行数: 492447\n", "删除行数: 733\n", "保留行数: 491714\n", "删除比例: 0.15%\n"]}], "source": ["import pandas as pd\n", "import numpy as np\n", "\n", "def find_and_remove_inf(csv_file, output_file):\n", "    \"\"\"\n", "    查找并删除包含inf/-inf值的行\n", "    \n", "    参数:\n", "        csv_file: 输入CSV文件路径\n", "        output_file: 输出CSV文件路径\n", "    \"\"\"\n", "    # 读取CSV文件\n", "    df = pd.read_csv(csv_file)\n", "    \n", "    # 记录原始行数\n", "    original_rows = len(df)\n", "    \n", "    # 查找包含inf/-inf的行\n", "    inf_mask = df.isin([np.inf, -np.inf]).any(axis=1)\n", "    inf_rows = df[inf_mask].index.tolist()\n", "    \n", "    if not inf_rows:\n", "        print(\"文件中未发现包含inf/-inf值的行\")\n", "        df.to_csv(output_file, index=False)\n", "        return\n", "    \n", "    # 显示检测结果\n", "    print(f\"发现 {len(inf_rows)} 行包含inf/-inf值\")\n", "    \n", "    # 删除这些行\n", "    df_cleaned = df[~inf_mask]\n", "    \n", "    # 保存处理后的数据\n", "    df_cleaned.to_csv(output_file, index=False)\n", "    \n", "    # 输出统计信息\n", "    print(f\"\\n处理完成，结果已保存到: {output_file}\")\n", "    print(f\"原始行数: {original_rows}\")\n", "    print(f\"删除行数: {len(inf_rows)}\")\n", "    print(f\"保留行数: {len(df_cleaned)}\")\n", "    print(f\"删除比例: {len(inf_rows)/original_rows:.2%}\")\n", "\n", "if __name__ == \"__main__\":\n", "    input_file = \"cb_factors5.csv\"    # 输入文件路径\n", "    output_file = \"cb_factors6.csv\"  # 输出文件路径\n", "    \n", "    find_and_remove_inf(input_file, output_file)"]}, {"cell_type": "code", "execution_count": 2, "id": "cc7e5dcc", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["处理完成，结果已保存到: cb_factors2.csv\n", "原始行数: 545285\n", "删除行数: 54488 (因其他列存在inf/空值)\n", "保留行数: 490797\n", "删除比例: 9.99%\n", "注意：列 'trade_date' 中的inf/空值会被保留\n"]}], "source": ["import pandas as pd\n", "import numpy as np\n", "\n", "def clean_csv_except_column(input_file, output_file, protected_column):\n", "    \"\"\"\n", "    删除除指定列外的所有inf/-inf/空值所在行\n", "    \n", "    参数:\n", "        input_file: 输入CSV文件路径\n", "        output_file: 输出CSV文件路径\n", "        protected_column: 要保护的列名（该列的inf/空值不会被删除）\n", "    \"\"\"\n", "    # 读取CSV文件\n", "    df = pd.read_csv(input_file)\n", "    \n", "    # 检查保护列是否存在\n", "    if protected_column not in df.columns:\n", "        print(f\"错误：文件中不存在列 '{protected_column}'\")\n", "        return\n", "    \n", "    # 记录原始行数\n", "    original_rows = len(df)\n", "    \n", "    # 1. 找出除保护列外的其他列\n", "    other_columns = [col for col in df.columns if col != protected_column]\n", "    \n", "    # 2. 检查这些列中的inf/-inf/空值\n", "    # 创建掩码：标记出其他列中有问题的行\n", "    mask = (\n", "        df[other_columns].isin([np.inf, -np.inf, np.nan, '', ' '])\n", "        .any(axis=1)\n", "    )\n", "    \n", "    # 3. 删除这些行\n", "    df_cleaned = df[~mask]\n", "    \n", "    # 保存处理后的数据\n", "    df_cleaned.to_csv(output_file, index=False)\n", "    \n", "    # 输出统计信息\n", "    removed_rows = original_rows - len(df_cleaned)\n", "    print(f\"处理完成，结果已保存到: {output_file}\")\n", "    print(f\"原始行数: {original_rows}\")\n", "    print(f\"删除行数: {removed_rows} (因其他列存在inf/空值)\")\n", "    print(f\"保留行数: {len(df_cleaned)}\")\n", "    print(f\"删除比例: {removed_rows/original_rows:.2%}\")\n", "    print(f\"注意：列 '{protected_column}' 中的inf/空值会被保留\")\n", "\n", "if __name__ == \"__main__\":\n", "    input_csv = \"cb_factors.csv\"       # 输入文件路径\n", "    output_csv = \"cb_factors2.csv\"    # 输出文件路径\n", "    protected_col = \"trade_date\"          # 要保护的列名（该列的inf/空值不删除）\n", "    \n", "    clean_csv_except_column(input_csv, output_csv, protected_col)"]}, {"cell_type": "code", "execution_count": null, "id": "a54b2fcc", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["CSV文件中的列名:\n", "[\n", "    'ts_code', 'trade_date', 'open', 'high', 'low', 'close', 'pct_chg', 'vol', 'amount', 'target_return',\n", "    'momentum_5d', 'ma_ratio_5d', 'volatility_5d', 'momentum_10d', 'ma_ratio_10d', 'volatility_10d', 'momentum_20d', 'ma_ratio_20d', 'volatility_20d', 'rsi_14d',\n", "    'macd', 'macd_signal', 'macd_hist', 'bb_ratio', 'atr', 'adaptive_ma', 'volume_weighted_momentum_10d', 'volume_weighted_momentum_20d', 'volatility_adjusted_momentum_10d', 'volatility_adjusted_momentum_20d',\n", "    'price_acceleration', 'price_efficiency_ratio', 'momentum_volatility_composite_10d', 'momentum_volatility_composite_20d', 'trend_strength_10d', 'trend_strength_20d', 'overextension_factor_10d', 'overextension_factor_20d', 'pullback_opportunity', 'pe_relative',\n", "    'pb_relative', 'mfi_14d', 'adosc', 'obv_ratio_10d', 'upper_shadow', 'lower_shadow', 'real_body_ratio', 'close_position', 'turnover_rate_anomaly_20d', 'size_factor',\n", "    'volume_ratio', 'skew_20d', 'kurt_20d', 'downside_risk_20d', 'fractal_indicator', 'natr', 'kdj_k', 'kdj_d', 'kdj_j', 'volatility_regime',\n", "    'obv', 'obv_trend', 'adx', 'plus_di', 'minus_di', 'di_diff', 'rsi', 'rsi_divergence', 'reaction_speed_factor', 'range_expansion',\n", "    'volume_price_trend', 'normalized_composite_momentum', 'smart_money', 'stealth_accumulation', 'exhaustion_indicator', 'price_velocity_factor', 'pressure_release', 'intraday_volatility_distribution', 'price_jump_detection', 'volume_concentration',\n", "    'order_imbalance', 'micro_momentum', 'information_ratio', 'price_discovery_efficiency', 'liquidity_consumption', 'quote_stability', 'market_depth_proxy', 'order_flow_toxicity', 'fear_index_proxy', 'greed_index_proxy',\n", "    'sentiment_transition', 'extreme_sentiment', 'sentiment_momentum', 'dynamic_support_resistance', 'strength_persistence', 'rsi_acceleration', 'cost_range', 'cost_std', 'price_vs_median', 'price_position',\n", "    'support_strength', 'resistance_strength', 'historical_position', 'cost_concentration', 'cost_skewness', 'price_vs_avg_cost', 'upper_pressure', 'heavy_pressure', 'cost_median_momentum', 'weight_avg_momentum',\n", "    'cost_trend', 'winner_momentum', 'winner_trend', 'winner_position', 'winner_zscore'\n", "]\n", "\n", "一共有 115 个列名\n"]}], "source": ["import pandas as pd\n", "\n", "def show_column_names(file_path):\n", "    \"\"\"\n", "    显示CSV文件的所有列名，按照Python数组格式打印，每行10个\n", "    \n", "    参数:\n", "        file_path (str): CSV文件路径\n", "    \"\"\"\n", "    try:\n", "        # 读取CSV文件\n", "        df = pd.read_csv(file_path)\n", "        columns = df.columns.tolist()\n", "        \n", "        # 打印Python数组格式的列名\n", "        print(\"CSV文件中的列名:\")\n", "        print(\"[\")\n", "        \n", "        # 每行打印10个元素\n", "        for i in range(0, len(columns), 10):\n", "            row = columns[i:i+10]\n", "            # 处理每行的最后一个元素是否有逗号\n", "            row_str = \", \".join(f\"'{col}'\" for col in row)\n", "            if i + 10 < len(columns):  # 不是最后一行就加逗号\n", "                row_str += \",\"\n", "            print(f\"    {row_str}\")\n", "            \n", "        print(\"]\")\n", "        \n", "        # 打印列名总数\n", "        print(f\"\\n一共有 {len(columns)} 个列名\")\n", "        \n", "        return columns\n", "    except FileNotFoundError:\n", "        print(f\"错误: 文件 '{file_path}' 未找到\")\n", "        return []\n", "    except Exception as e:\n", "        print(f\"发生错误: {str(e)}\")\n", "        return []\n", "\n", "# 使用示例\n", "if __name__ == \"__main__\":\n", "    file_path = 'tushare_data_cyb/stock_factors_cyb_train.csv'\n", "    show_column_names(file_path)"]}, {"cell_type": "code", "execution_count": 1, "id": "15cdb53b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["CSV文件的前10行：\n", "     ts_code  trade_date   open   high    low  close  pct_chg        vol  \\\n", "0  300001.SZ  2021-02-26  29.50  30.10  28.86  29.67  -2.5616  163634.78   \n", "1  300001.SZ  2021-03-01  30.00  30.28  29.56  29.97   1.0111  139290.92   \n", "2  300001.SZ  2021-03-02  30.10  30.78  29.26  30.11   0.4671  217647.04   \n", "3  300001.SZ  2021-03-03  29.88  31.00  29.60  30.84   2.4244  176805.45   \n", "4  300001.SZ  2021-03-04  30.56  30.78  29.13  29.37  -4.7665  197367.24   \n", "5  300001.SZ  2021-03-05  28.70  29.77  28.38  29.37   0.0000  140567.50   \n", "6  300001.SZ  2021-03-08  29.59  29.64  26.20  26.20 -10.7933  322736.78   \n", "7  300001.SZ  2021-03-09  26.05  26.48  24.97  25.18  -3.8931  303077.45   \n", "8  300001.SZ  2021-03-10  25.75  26.37  25.35  25.50   1.2708  154822.43   \n", "9  300001.SZ  2021-03-11  25.63  26.35  25.20  26.26   2.9804  143382.01   \n", "\n", "       amount  target_return  ...  gap_and_go  std_dev_breakout  \\\n", "0  483751.831       2.055949  ...           0                 0   \n", "1  416340.398       2.702703  ...           0                 0   \n", "2  654590.833       2.955829  ...           0                 0   \n", "3  539886.573      -0.194553  ...           0                 0   \n", "4  585819.718       1.361934  ...           0                 0   \n", "5  409515.640       0.919305  ...           0                 0   \n", "6  890976.342       1.068702  ...           0                 0   \n", "7  778849.913       4.725973  ...           0                 0   \n", "8  398184.377       3.333333  ...           0                 0   \n", "9  372142.843       3.008378  ...           0                 0   \n", "\n", "   three_white_soldiers  rising_window  hammer_signal_in_uptrend  \\\n", "0                     0              0                         0   \n", "1                     0              0                         0   \n", "2                     0              0                         0   \n", "3                     0              0                         0   \n", "4                     0              0                         0   \n", "5                     0              0                         0   \n", "6                     0              0                         0   \n", "7                     0              0                         0   \n", "8                     0              0                         0   \n", "9                     0              0                         0   \n", "\n", "   closing_marubozu  intraday_buying_momentum  relative_volume  \\\n", "0                 0                 13.709677         0.549711   \n", "1                 0                 -4.166667         0.492048   \n", "2                 0                  0.657895         0.780200   \n", "3                 0                 68.571429         0.648955   \n", "4                 0                -72.121212         0.759208   \n", "5                 0                 48.201439         0.570940   \n", "6                 0                -98.546512         1.317517   \n", "7                 0                -57.615894         1.227237   \n", "8                 0                -24.509804         0.639198   \n", "9                 0                 54.782609         0.610033   \n", "\n", "   aroon_up_dominant  trix_signal  \n", "0                100     0.000000  \n", "1                  0    -2.369157  \n", "2                  0    -3.906945  \n", "3                  0    -4.132447  \n", "4                  0    -4.535106  \n", "5                100    -4.713711  \n", "6                100    -7.039125  \n", "7                100   -10.682232  \n", "8                100   -13.951127  \n", "9                100   -15.636018  \n", "\n", "[10 rows x 133 columns]\n"]}], "source": ["import pandas as pd\n", "\n", "# 读取CSV文件\n", "file_path = 'tushare_data\\stock_factors_train.csv'  # 替换为你的CSV文件路径\n", "data = pd.read_csv(file_path)\n", "\n", "# 打印前10行\n", "print(\"CSV文件的前10行：\")\n", "print(data.head(10))"]}, {"cell_type": "code", "execution_count": null, "id": "707ae15b", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "\n", "def remove_columns(file_path, columns_to_remove, output_file=None):\n", "    \"\"\"\n", "    从CSV文件中删除指定的列\n", "    \n", "    参数:\n", "        file_path (str): 输入CSV文件路径\n", "        columns_to_remove (list): 要删除的列名列表\n", "        output_file (str): 输出文件路径(可选)，如果为None则覆盖原文件\n", "    \"\"\"\n", "    try:\n", "        # 读取CSV文件\n", "        df = pd.read_csv(file_path)\n", "        \n", "        # 检查要删除的列是否存在\n", "        existing_columns = set(df.columns)\n", "        columns_to_remove = [col for col in columns_to_remove if col in existing_columns]\n", "        \n", "        if not columns_to_remove:\n", "            print(\"没有找到要删除的列\")\n", "            return\n", "            \n", "        # 删除指定列\n", "        df = df.drop(columns=columns_to_remove)\n", "        \n", "        # 保存结果\n", "        output_path = output_file if output_file else file_path\n", "        df.to_csv(output_path, index=False)\n", "        \n", "        print(f\"已成功删除列: {', '.join(columns_to_remove)}\")\n", "        print(f\"结果已保存到: {output_path}\")\n", "    except FileNotFoundError:\n", "        print(f\"错误: 文件 '{file_path}' 未找到\")\n", "    except Exception as e:\n", "        print(f\"发生错误: {str(e)}\")\n", "\n", "# 使用示例\n", "if __name__ == \"__main__\":\n", "    file_path = 'cb_factors_train.csv'\n", "    \n", "    to_remove = input(\"请输入要删除的列名(多个列用逗号分隔): \").strip().split(',')\n", "    to_remove = [col.strip() for col in to_remove if col.strip()]\n", "        \n", "    if to_remove:\n", "        output_file = input(\"请输入输出文件路径(留空则覆盖原文件): \").strip() or None\n", "        remove_columns(file_path, to_remove, output_file)"]}, {"cell_type": "code", "execution_count": 6, "id": "5897d2d6", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["处理完成。已删除两年前的数据，结果保存到 cb_factors_2.csv\n", "原始数据行数: 310131\n", "处理后行数: 115904\n"]}], "source": ["import pandas as pd\n", "from datetime import datetime, timedelta\n", "\n", "def remove_old_data(input_file, output_file, date_column):\n", "    \"\"\"\n", "    读取CSV文件，删除两年前的数据，并保存到新文件\n", "    \n", "    参数:\n", "        input_file (str): 输入CSV文件路径\n", "        output_file (str): 输出CSV文件路径\n", "        date_column (str): 包含日期的列名\n", "    \"\"\"\n", "    try:\n", "        # 读取CSV文件\n", "        df = pd.read_csv(input_file)\n", "        \n", "        # 确保日期列存在\n", "        if date_column not in df.columns:\n", "            raise ValueError(f\"列 '{date_column}' 不存在于CSV文件中\")\n", "            \n", "        # 将日期列转换为datetime类型\n", "        df[date_column] = pd.to_datetime(df[date_column])\n", "        \n", "        # 计算两年前的日期\n", "        two_years_ago = datetime.now() - <PERSON><PERSON><PERSON>(days=400)  # 大约2年\n", "        \n", "        # 筛选出两年内的数据\n", "        filtered_df = df[df[date_column] >= two_years_ago]\n", "        \n", "        # 保存到新文件\n", "        filtered_df.to_csv(output_file, index=False)\n", "        \n", "        print(f\"处理完成。已删除两年前的数据，结果保存到 {output_file}\")\n", "        print(f\"原始数据行数: {len(df)}\")\n", "        print(f\"处理后行数: {len(filtered_df)}\")\n", "        \n", "    except Exception as e:\n", "        print(f\"发生错误: {str(e)}\")\n", "\n", "# 使用示例\n", "if __name__ == \"__main__\":\n", "    input_csv = \"cb_factors.csv\"      # 输入CSV文件路径\n", "    output_csv = \"cb_factors_2.csv\"    # 输出CSV文件路径\n", "    date_col = \"trade_date\"            # 包含日期的列名\n", "    \n", "    remove_old_data(input_csv, output_csv, date_col)"]}], "metadata": {"kernelspec": {"display_name": "v8new", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.10"}}, "nbformat": 4, "nbformat_minor": 5}